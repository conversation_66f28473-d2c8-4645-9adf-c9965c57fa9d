import dotenv from 'dotenv';
import mysql from 'mysql2/promise';
import { v4 as uuidv4 } from 'uuid';
import logger from '../src/config/logger.js';

// Load environment variables
dotenv.config({ path: '.env' });

/**
 * <PERSON>ript to migrate LLM models from local database to production
 * This script reads all LLM models from local database and creates them on production
 */

// Local database configuration
const LOCAL_DB_CONFIG = {
  host: process.env.CHAT_DB_HOST || 'localhost',
  user: process.env.CHAT_DB_USER || 'root',
  password: process.env.CHAT_DB_PASSWORD || '',
  database: process.env.CHAT_DB_NAME || 'infini_ai_user_chat_recs',
  port: process.env.CHAT_DB_PORT || 3306
};

// Production database configuration
const PRODUCTION_DB_CONFIG = {
  host: process.env.PROD_CHAT_DB_HOST || process.env.CHAT_DB_HOST,
  user: process.env.PROD_CHAT_DB_USER || process.env.CHAT_DB_USER,
  password: process.env.PROD_CHAT_DB_PASSWORD || process.env.CHAT_DB_PASSWORD,
  database: process.env.PROD_CHAT_DB_NAME || process.env.CHAT_DB_NAME,
  port: process.env.PROD_CHAT_DB_PORT || process.env.CHAT_DB_PORT || 3306
};

/**
 * Read all LLM models from local database
 */
async function readLocalLLMModels() {
  let connection;
  try {
    logger.info('Connecting to local database...');
    connection = await mysql.createConnection(LOCAL_DB_CONFIG);
    
    const [rows] = await connection.execute('SELECT * FROM llm_models ORDER BY provider, model_name');
    
    logger.info(`Found ${rows.length} LLM models in local database`);
    
    // Group by provider for logging
    const providerCounts = {};
    rows.forEach(model => {
      providerCounts[model.provider] = (providerCounts[model.provider] || 0) + 1;
    });
    
    logger.info('Models by provider:');
    Object.entries(providerCounts).forEach(([provider, count]) => {
      logger.info(`  ${provider}: ${count} models`);
    });
    
    return rows;
  } catch (error) {
    logger.error('Error reading local LLM models:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

/**
 * Create LLM models in production database
 */
async function createProductionLLMModels(models) {
  let connection;
  try {
    logger.info('Connecting to production database...');
    connection = await mysql.createConnection(PRODUCTION_DB_CONFIG);
    
    // Check if table exists
    const [tables] = await connection.execute(
      "SHOW TABLES LIKE 'llm_models'"
    );
    
    if (tables.length === 0) {
      logger.error('llm_models table does not exist in production database');
      throw new Error('Production table not found');
    }
    
    // Check existing models in production
    const [existingModels] = await connection.execute(
      'SELECT model_id FROM llm_models'
    );
    
    const existingModelIds = new Set(existingModels.map(m => m.model_id));
    logger.info(`Found ${existingModelIds.size} existing models in production`);
    
    let createdCount = 0;
    let updatedCount = 0;
    let skippedCount = 0;
    
    for (const model of models) {
      try {
        // Generate new UUID for production (avoid ID conflicts)
        const newId = uuidv4();
        
        if (existingModelIds.has(model.model_id)) {
          // Update existing model
          const updateQuery = `
            UPDATE llm_models SET
              provider = ?,
              model_name = ?,
              display_name = ?,
              description = ?,
              capabilities = ?,
              logo_url = ?,
              logo_attachment_secure_id = ?,
              api_key = ?,
              pricing = ?,
              context_window = ?,
              max_output_tokens = ?,
              supports_vision = ?,
              supports_audio = ?,
              supports_code_execution = ?,
              is_active = ?,
              updated_at = CURRENT_TIMESTAMP
            WHERE model_id = ?
          `;
          
          await connection.execute(updateQuery, [
            model.provider,
            model.model_name,
            model.display_name,
            model.description,
            JSON.stringify(model.capabilities),
            model.logo_url,
            model.logo_attachment_secure_id,
            model.api_key,
            JSON.stringify(model.pricing),
            model.context_window,
            model.max_output_tokens,
            model.supports_vision,
            model.supports_audio,
            model.supports_code_execution,
            model.is_active,
            model.model_id
          ]);
          
          updatedCount++;
          logger.info(`✓ Updated: ${model.display_name} (${model.model_id})`);
        } else {
          // Insert new model
          const insertQuery = `
            INSERT INTO llm_models (
              id, provider, model_name, model_id, display_name, description,
              capabilities, logo_url, logo_attachment_secure_id, api_key,
              pricing, context_window, max_output_tokens, supports_vision,
              supports_audio, supports_code_execution, is_active,
              created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `;
          
          await connection.execute(insertQuery, [
            newId,
            model.provider,
            model.model_name,
            model.model_id,
            model.display_name,
            model.description,
            JSON.stringify(model.capabilities),
            model.logo_url,
            model.logo_attachment_secure_id,
            model.api_key,
            JSON.stringify(model.pricing),
            model.context_window,
            model.max_output_tokens,
            model.supports_vision,
            model.supports_audio,
            model.supports_code_execution,
            model.is_active,
            new Date(),
            new Date()
          ]);
          
          createdCount++;
          logger.info(`✓ Created: ${model.display_name} (${model.model_id})`);
        }
      } catch (modelError) {
        logger.error(`✗ Error processing ${model.display_name}:`, modelError.message);
        skippedCount++;
      }
    }
    
    logger.info('\n=== Migration Summary ===');
    logger.info(`Total models processed: ${models.length}`);
    logger.info(`Created: ${createdCount}`);
    logger.info(`Updated: ${updatedCount}`);
    logger.info(`Skipped/Failed: ${skippedCount}`);
    
    return { createdCount, updatedCount, skippedCount };
  } catch (error) {
    logger.error('Error creating production LLM models:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

/**
 * Verify production models
 */
async function verifyProductionModels() {
  let connection;
  try {
    logger.info('\nVerifying production models...');
    connection = await mysql.createConnection(PRODUCTION_DB_CONFIG);
    
    const [models] = await connection.execute(
      'SELECT provider, COUNT(*) as count FROM llm_models GROUP BY provider ORDER BY provider'
    );
    
    logger.info('Production models by provider:');
    models.forEach(({ provider, count }) => {
      logger.info(`  ${provider}: ${count} models`);
    });
    
    const [total] = await connection.execute('SELECT COUNT(*) as total FROM llm_models');
    logger.info(`Total models in production: ${total[0].total}`);
    
  } catch (error) {
    logger.error('Error verifying production models:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

/**
 * Main migration function
 */
async function migrateLLMModels() {
  try {
    logger.info('=== Starting LLM Models Migration to Production ===\n');
    
    // Validate configuration
    if (!PRODUCTION_DB_CONFIG.host || !PRODUCTION_DB_CONFIG.user) {
      throw new Error('Production database configuration is incomplete. Please set PROD_CHAT_DB_* environment variables.');
    }
    
    // Read local models
    const localModels = await readLocalLLMModels();
    
    if (localModels.length === 0) {
      logger.warn('No models found in local database. Nothing to migrate.');
      return;
    }
    
    // Create/update production models
    const result = await createProductionLLMModels(localModels);
    
    // Verify production models
    await verifyProductionModels();
    
    logger.info('\n=== Migration completed successfully! ===');
    
  } catch (error) {
    logger.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
migrateLLMModels();
