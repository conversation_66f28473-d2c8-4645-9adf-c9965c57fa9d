import dotenv from 'dotenv';
import mysql from 'mysql2/promise';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import logger from '../src/config/logger.js';

// Load environment variables
dotenv.config({ path: '.env' });

/**
 * <PERSON>ript to export LLM models from local database to SQL file
 * This creates INSERT statements that can be run on production
 */

const LOCAL_DB_CONFIG = {
  host: process.env.CHAT_DB_HOST || 'localhost',
  user: process.env.CHAT_DB_USER || 'root',
  password: process.env.CHAT_DB_PASSWORD || '',
  database: process.env.CHAT_DB_NAME || 'infini_ai_user_chat_recs',
  port: process.env.CHAT_DB_PORT || 3306
};

/**
 * Escape SQL string values
 */
function escapeSQLString(value) {
  if (value === null || value === undefined) {
    return 'NULL';
  }
  if (typeof value === 'string') {
    return `'${value.replace(/'/g, "''").replace(/\\/g, '\\\\')}'`;
  }
  if (typeof value === 'boolean') {
    return value ? '1' : '0';
  }
  if (typeof value === 'object') {
    return `'${JSON.stringify(value).replace(/'/g, "''").replace(/\\/g, '\\\\')}'`;
  }
  return value;
}

/**
 * Generate SQL INSERT statements for LLM models
 */
async function exportLLMModelsToSQL() {
  let connection;
  try {
    logger.info('Connecting to local database...');
    connection = await mysql.createConnection(LOCAL_DB_CONFIG);
    
    const [rows] = await connection.execute('SELECT * FROM llm_models ORDER BY provider, model_name');
    
    logger.info(`Found ${rows.length} LLM models to export`);
    
    // Generate SQL content
    let sqlContent = `-- LLM Models Migration Script
-- Generated on: ${new Date().toISOString()}
-- Total models: ${rows.length}

USE infini_ai_user_chat_recs;

-- Disable foreign key checks temporarily
SET FOREIGN_KEY_CHECKS = 0;

-- Clear existing models (optional - uncomment if you want to start fresh)
-- DELETE FROM llm_models;

`;

    // Add INSERT statements
    for (const model of rows) {
      // Generate new UUID for production
      const newId = uuidv4();
      
      sqlContent += `-- ${model.provider}: ${model.display_name}
INSERT INTO llm_models (
  id, provider, model_name, model_id, display_name, description,
  capabilities, logo_url, logo_attachment_secure_id, api_key,
  pricing, context_window, max_output_tokens, supports_vision,
  supports_audio, supports_code_execution, is_active,
  created_at, updated_at
) VALUES (
  ${escapeSQLString(newId)},
  ${escapeSQLString(model.provider)},
  ${escapeSQLString(model.model_name)},
  ${escapeSQLString(model.model_id)},
  ${escapeSQLString(model.display_name)},
  ${escapeSQLString(model.description)},
  ${escapeSQLString(model.capabilities)},
  ${escapeSQLString(model.logo_url)},
  ${escapeSQLString(model.logo_attachment_secure_id)},
  ${escapeSQLString(model.api_key)},
  ${escapeSQLString(model.pricing)},
  ${model.context_window || 'NULL'},
  ${model.max_output_tokens || 'NULL'},
  ${model.supports_vision ? 1 : 0},
  ${model.supports_audio ? 1 : 0},
  ${model.supports_code_execution ? 1 : 0},
  ${model.is_active ? 1 : 0},
  NOW(),
  NOW()
) ON DUPLICATE KEY UPDATE
  provider = VALUES(provider),
  model_name = VALUES(model_name),
  display_name = VALUES(display_name),
  description = VALUES(description),
  capabilities = VALUES(capabilities),
  logo_url = VALUES(logo_url),
  logo_attachment_secure_id = VALUES(logo_attachment_secure_id),
  api_key = VALUES(api_key),
  pricing = VALUES(pricing),
  context_window = VALUES(context_window),
  max_output_tokens = VALUES(max_output_tokens),
  supports_vision = VALUES(supports_vision),
  supports_audio = VALUES(supports_audio),
  supports_code_execution = VALUES(supports_code_execution),
  is_active = VALUES(is_active),
  updated_at = NOW();

`;
    }

    sqlContent += `
-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Verify the import
SELECT 
  provider,
  COUNT(*) as model_count
FROM llm_models 
GROUP BY provider 
ORDER BY provider;

SELECT COUNT(*) as total_models FROM llm_models;
`;

    // Write to file
    const outputPath = path.join(process.cwd(), 'scripts', 'llm-models-production.sql');
    fs.writeFileSync(outputPath, sqlContent);
    
    logger.info(`SQL export completed successfully!`);
    logger.info(`File saved to: ${outputPath}`);
    logger.info(`\nTo import on production server, run:`);
    logger.info(`mysql -u username -p database_name < ${outputPath}`);
    
    // Show summary
    const providerCounts = {};
    rows.forEach(model => {
      providerCounts[model.provider] = (providerCounts[model.provider] || 0) + 1;
    });
    
    logger.info('\nExported models by provider:');
    Object.entries(providerCounts).forEach(([provider, count]) => {
      logger.info(`  ${provider}: ${count} models`);
    });
    
  } catch (error) {
    logger.error('Error exporting LLM models:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the export
exportLLMModelsToSQL();
