# Production Database Configuration for LLM Models Migration
# Copy this file to .env.production and fill in your production database details

# Production Chat Database Configuration
PROD_CHAT_DB_HOST=your-production-db-host
PROD_CHAT_DB_USER=your-production-db-user
PROD_CHAT_DB_PASSWORD=your-production-db-password
PROD_CHAT_DB_NAME=infini_ai_user_chat_recs
PROD_CHAT_DB_PORT=3306

# Alternative: If you want to use the same environment variables as local
# Just set these to your production values:
# CHAT_DB_HOST=your-production-db-host
# CHAT_DB_USER=your-production-db-user
# CHAT_DB_PASSWORD=your-production-db-password
# CHAT_DB_NAME=infini_ai_user_chat_recs
# CHAT_DB_PORT=3306
